<template>
  <UContainer class="mt-2">
    <div class="flex flex-col gap-4 lg:flex-row">
      <div class="w-full">
        <div class="space-y-2">
          <!--                    <FiltersSubMenu /> -->
          <FiltersCategories />
          <div v-if="false" class="flex flex-wrap gap-1">
            <UButton size="xs" color="neutral" variant="soft"> Пистолет пм </UButton>
            <UButton size="xs" color="neutral" variant="soft"> Охолощенное оружие </UButton>
            <UButton size="xs" color="neutral" variant="soft"> Травматический пистолет </UButton>
            <UButton size="xs" color="neutral" variant="soft"> Купить карабин для охоты </UButton>
            <UButton size="xs" color="neutral" variant="soft"> Вепрь 12 молот </UButton>
          </div>

          <AlertsBase />
        </div>

        <h1 v-if="posts?.seo?.title" class="mt-4 text-3xl leading-6 font-bold">
          {{ posts.seo.title }}
        </h1>
        <p v-if="posts?.seo?.content" class="my-2 text-base">
          {{ posts.seo.content }}
        </p>

        <div
          class="mt-3 flex flex-col justify-between gap-1 border-t border-[var(--ui-border)]/60 pt-3 md:flex-row md:items-center"
        >
          <p class="md:text-md text-sm font-semibold">Рекомендации для вас</p>
          <FiltersOrderBy />
        </div>

        <div class="mt-2 grid w-full grid-cols-1 gap-4 md:grid-cols-3">
          <template v-if="status === 'pending'">
            <Skeleton v-for="i in 21" :key="i" />
          </template>
          <template v-else>
            <CardItem v-for="(item, n) in posts?.data" :key="n" :position="n" :item="item" />
          </template>
        </div>

        <LazyPagination
          :meta="posts?.meta"
          :disabled="status === 'pending'"
          @next-page="nextPage"
        />
      </div>
      <div class="w-full space-y-4 lg:w-110">
        <NewsWidget :limit="10" />
        <div v-if="false" class="space-y-2">
          <h3 class="text-base font-semibold text-pretty text-(--ui-text-highlighted)">
            Популярные обсуждения
          </h3>
          <ULink
            to="/"
            class="block space-y-2 rounded-xl bg-gray-50 p-4 text-left text-sm hover:bg-gray-100 dark:bg-zinc-800 dark:hover:bg-zinc-700"
          >
            <h3 class="leading-none text-black dark:text-white">
              Вопрос по эволюции патронов кольцевого воспламенения
            </h3>
            <p class="line-clamp-3 text-[var(--ui-text-muted)]">
              Поиск по форуму нашел, что в ранних патронах Флобера не было выраженной закраины, а
              только расширение, чтобы патрон не проваливался в ствол и было чего сминать курку
            </p>
            <UUser
              size="3xs"
              name="Ротмистр Чачу"
              description="28-6-2024 12:38"
              :avatar="{ src: 'https://i.pravatar.cc/50?u=Ротмистр Чачу' }"
            />
          </ULink>
          <ULink
            to="/"
            class="block space-y-2 rounded-xl bg-gray-50 p-4 text-left text-sm hover:bg-gray-100 dark:bg-zinc-800 dark:hover:bg-zinc-700"
          >
            <h3 class="leading-none text-black dark:text-white">
              Перествол ВПО-209 366ТКМ в СКС 7, 62
            </h3>
            <p class="line-clamp-3 text-[var(--ui-text-muted)]">
              Имею такой балласт в виде ВПО-209 который в свое время взял для стажа. В этой связи
              вопрос - возможна ли его легальная модернизация до
            </p>
            <UUser
              size="3xs"
              name="jacker2000"
              description="3-2-2025 12:03"
              :avatar="{ src: 'https://i.pravatar.cc/50?u=jacker2000' }"
            />
          </ULink>

          <UButton size="sm" block variant="soft"> Обсуждения </UButton>
        </div>
        <LazyAdsVip v-if="posts?.vipAds?.length" :items="posts?.vipAds" />
      </div>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
const client = useSanctumClient();
const route = useRoute();

const { data: posts, status } = await useAsyncData(
  `posts:${route.query?.page ?? 1}`,
  () =>
    client("/posts", {
      params: {
        page: route.query?.page ?? 1,
        ...route.query
      }
    }),
  {
    watch: [route]
  }
);

useSeoMeta({
  title: posts.value?.seo?.title,
  ogTitle: posts.value?.seo?.title,
  description: posts.value?.seo?.meta_description,
  ogDescription: posts.value?.seo?.meta_description
});

const nextPage = async () => {
  const nextPage = posts.value.meta.current_page + 1;
  const response = await client("/posts", {
    params: {
      ...route.query,
      ...{ page: nextPage }
    }
  });

  if (response?.data?.length) {
    const newData = {
      data: undefined,
      meta: undefined,
      vipAds: undefined
    };

    newData.data = [...posts.value.data, ...response.data]; // Добавляем новые данные к текущим
    newData.meta = response.meta; // Обновляем номер страницы
    newData.vipAds = response.vipAds;

    posts.value = newData;
  }
};
</script>
