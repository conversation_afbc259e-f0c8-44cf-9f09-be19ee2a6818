<template>
  <div v-if="!isPageLoaded" class="flex h-full w-full items-center justify-center">
    <UIcon name="i-lucide-loader-circle" class="size-20 animate-spin text-[var(--ui-text-muted)]" />
  </div>
  <div v-if="isPageLoaded" class="mt-4 space-y-6">
    <UModal v-if="isSuccessModal" open :close="false">
      <template #body>
        <UIcon name="i-lucide-laptop-minimal-check" class="text-info size-20" />
        <h3 class="mb-2 text-2xl font-bold">Ваше объявление отправлено на модерацию.</h3>
        <p>
          Мы проверим его на соответствие официальному законодательству и опубликуем в течении 24
          часов.
        </p>
      </template>
      <template #footer>
        <UButton to="/profile">Перейти в профиль</UButton>
      </template>
    </UModal>
    <div v-if="isAutoSaveLoading" class="fixed right-2 bottom-1 text-xs">
      <UIcon name="i-lucide-loader-circle" class="animate-spin text-[var(--ui-text-muted)]" />
    </div>
    <UContainer v-if="state.step <= 2" class="max-w-3xl">
      <div>
        <h1
          class="mb-2 flex items-center gap-1.5 text-xl font-semibold text-pretty text-(--ui-text-highlighted)"
        >
          Новое объявление
        </h1>
        <p class="text-md">
          Вы можете добавить объявление о продаже оружия или снаряжения без регистрации на сайте.
          Реквизиты для управления объявлением будут отправлены Вам на почту.
        </p>
      </div>
    </UContainer>
    <UContainer v-else-if="state.title" class="max-w-3xl">
      <h1 class="text-3xl font-semibold text-pretty text-(--ui-text-highlighted)">
        Продам: {{ state.title }}
      </h1>
      <p v-if="state?.category?.label" class="mb-2 text-(--ui-text-muted)">
        В категории: {{ state.category.label }}
      </p>
      <p v-if="state?.city?.label" class="mb-2 text-(--ui-text-muted)">
        Город: {{ state.city.label }}
      </p>
    </UContainer>

    <UContainer class="max-w-3xl space-y-4">
      <UForm
        v-if="state.step >= 1"
        id="step-1"
        :schema="schemaStepCategory"
        :state="state as any"
        class="space-y-4"
        :class="state.step > 1 ? 'pointer-events-none opacity-40 hover:opacity-60' : ''"
      >
        <CardEditCategories
          v-model="state.category"
          :is_active="state.step === 1"
          @update:model-value="nextStep"
        />
      </UForm>

      <UForm
        v-if="state.step >= 2"
        id="step-2"
        :state="state as any"
        :schema="schemaStepTitle"
        class="space-y-4"
        :class="state.step > 2 ? 'opacity-40 hover:opacity-60' : ''"
        :disabled="state.step > 2"
        @submit="createPost"
      >
        <CardEditTitle
          v-model="state.title"
          :is_active="state.step === 2"
          :is-loading="isLoading"
          @back="setStep(1)"
        />
      </UForm>

      <UForm
        v-if="state.step >= 3"
        id="step-3"
        :state="state as any"
        :validate="validateStepParameters"
        :class="state.step > 3 ? 'opacity-40 hover:opacity-60' : ''"
        :disabled="state.step > 3"
        @submit="nextStep"
      >
        <UCard :ui="{ body: 'space-y-4' }">
<!--          <template #header> Параметры объявления </template>-->
          <UFormField size="xl" name="year" label="Год выпуска">
            <UInput
              v-model="state.year"
              class="w-full"
              placeholder="2022"
              type="number"
              :min="1900"
              :max="new Date().getFullYear()"
            />
          </UFormField>
          <UFormField
            v-for="(attrs, name) in attributes"
            :key="name"
            size="xl"
            :name="name"
            :label="attrs.label"
            :required="attrs.required"
          >
            <USelect
              v-model="state.attributes[name]"
              class="w-full"
              :placeholder="attrs.placeholder"
              :items="getValues(name, attrs.values || [])"
            />
          </UFormField>

          <template v-if="state.step === 3" #footer>
            <div class="flex gap-2">
              <UButton size="xl" type="submit" :loading="isLoading" color="success">
                Продолжить
              </UButton>
              <UButton size="xl" variant="link" color="neutral" @click="setStep(2)">
                Назад
              </UButton>
            </div>
          </template>
        </UCard>
      </UForm>

      <UForm
        v-if="state.step >= 4"
        id="step-4"
        :state="state as any"
        :validate="validateStepAddress"
        :class="state.step > 4 ? 'opacity-40 hover:opacity-60' : ''"
        :disabled="state.step > 4"
        @submit="nextStep"
      >
        <UCard :ui="{ body: 'space-y-4' }">
<!--          <template #header> Параметры объявления </template>-->
          <UFormField required size="xl" name="city" label="Город продажи">
            <USelectMenu
              v-model="state.city"
              :search-input="{ placeholder: 'Укажите город...' }"
              size="xl"
              class="w-full"
              :items="cityStore.cities"
            />
          </UFormField>

          <UFormField size="xl" name="address" label="Место осмотра:">
            <UButtonGroup v-if="state.address" class="w-full">
              <UInput class="w-full !cursor-default" :value="state.address" disabled />
              <UButton icon="i-lucide-x" square color="error" @click="state.address = null" />
            </UButtonGroup>
            <AddressInput
              v-else
              id="address-data"
              v-model="state.data_address"
              :city="state.city"
            />
          </UFormField>

          <div v-if="state.category?.registration" class="space-y-4">
            <UFormField
              size="xl"
              name="registration_type"
              required
              label="Переоформление:"
              description="Важнейший этап приобретения оружия у физического лица – правильное оформление документов"
            >
              <URadioGroup
                v-model="state.registration_type"
                class="mt-4"
                variant="table"
                :items="registrationItems"
              />
            </UFormField>

            <UFormField
              v-if="state.city && state.registration_type"
              required
              size="xl"
              name="registration_address"
              label="Укажите адрес:"
            >
              <UButtonGroup v-if="state.registration_address" class="w-full">
                <UInput
                  class="w-full !cursor-default"
                  :value="state.registration_address"
                  disabled
                />
                <UButton
                  icon="i-lucide-x"
                  square
                  color="error"
                  @click="state.registration_address = null"
                />
              </UButtonGroup>
              <AddressInput
                v-else
                id="address-place"
                v-model="state.data_registration_address"
                :city="state.city"
              />
            </UFormField>
          </div>
          <div v-else class="space-y-4">
            <UFormField required size="xl" name="can_ship">
              <UCheckbox
                v-model="state.can_ship"
                label="Возможна отправка почтой"
                description="Укажите, если вы готовы отправить почтой"
              />
            </UFormField>
          </div>

          <template v-if="state.step === 4" #footer>
            <div class="flex gap-2">
              <UButton size="xl" type="submit" :loading="isLoading" color="success">
                Продолжить
              </UButton>
              <UButton size="xl" variant="link" color="neutral" @click="setStep(3)">
                Назад
              </UButton>
            </div>
          </template>
        </UCard>
      </UForm>

      <UForm
        v-if="state.step >= 5"
        id="step-5"
        :state="state as any"
        :schema="schemaStepDescription"
        :class="state.step > 5 ? 'opacity-40 hover:opacity-60' : ''"
        :disabled="state.step > 5"
        @submit="nextStep"
      >
        <UCard :ui="{ body: 'space-y-4' }">
<!--          <template #header> Описание объявления </template>-->
          <UFormField
            size="xl"
            required
            name="description"
            label="Подробное описание:"
            description="Опишите свой товар, его состояние, и другую полезную информацию. Укажите способ передачи товара покупателю. Чем больше информации вы укажите, тем более интересным будет объявление с точки зрения покупателя"
          >
            <UTextarea v-model="state.description" class="w-full" :rows="6" autofocus autoresize />
          </UFormField>

          <template v-if="state.step === 5" #footer>
            <div class="flex gap-2">
              <UButton size="xl" type="submit" :loading="isLoading" color="success">
                Продолжить
              </UButton>
              <UButton size="xl" variant="link" color="neutral" @click="setStep(4)">
                Назад
              </UButton>
            </div>
          </template>
        </UCard>
      </UForm>

      <UForm
        v-if="state.step >= 6"
        id="step-6"
        :state="state as any"
        :validate="validateStepImages"
        :class="state.step > 6 ? 'pointer-events-none opacity-40 hover:opacity-60' : ''"
        :disabled="state.step > 6"
        @submit="uploadImages"
      >
        <UCard :ui="{ body: 'space-y-4' }">
<!--          <template #header> Фотографии объявления </template>-->
          <UFormField name="images">
            <CardEditPhoto v-model="state.images" :slug="state.slug || ''" :disabled="isLoading" />
          </UFormField>

          <template v-if="state.step === 6" #footer>
            <div class="flex gap-2">
              <UButton size="xl" type="submit" :loading="isLoading" color="success">
                Продолжить
              </UButton>
              <UButton size="xl" variant="link" color="neutral" @click="setStep(5)">
                Назад
              </UButton>
            </div>
          </template>
        </UCard>
      </UForm>

      <UForm
        v-if="state.step === 7"
        id="step-7"
        :state="state as any"
        :schema="schemaStepPrice"
        :class="state.step > 7 ? 'opacity-40 hover:opacity-60' : ''"
        @submit="finalStep"
      >
        <UCard :ui="{ body: 'space-y-4' }">
          <template #header> Стоимость объявления </template>
          <UFormField
            size="xl"
            name="price"
            label="Цена"
            help="Введите правильную и полную стоимость Вашего товара. Если Вы оставите поле пустым или напишете 0, будет показана 'Цена договорная'"
            description="Если Вы укажете неверную цену, Ваше объявление и Ваш аккаунт могут быть заблокированы после жалоб пользователей."
          >
            <UInput v-model="state.price" type="number" class="w-full" />
          </UFormField>

          <UFormField required size="xl" name="is_trade">
            <UCheckbox
              v-model="state.is_trade"
              label="Торг уместен"
              description="Укажите, если вы готовы торговаться"
            />
          </UFormField>

          <UFormField required size="xl" name="is_rebate">
            <UCheckbox
              v-model="state.is_rebate"
              label="Обмен возможен"
              description="Укажите, если вы готовы рассмореть обмен"
            />
          </UFormField>

          <template v-if="state.step === 7" #footer>
            <div class="flex items-center gap-2">
              <UButton
                v-if="isAuthenticated"
                size="xl"
                type="submit"
                :loading="isLoading"
                icon="i-lucide-sparkles"
                color="success"
              >
                Опубликовать
              </UButton>
              <UButton
                v-else
                size="xl"
                type="submit"
                :loading="isLoading"
                icon="i-lucide-user"
                color="neutral"
              >
                Войдите, чтобы опубликовать
              </UButton>
              <UButton size="xl" variant="link" color="neutral" @click="setStep(6)">
                Назад
              </UButton>
            </div>
          </template>
        </UCard>
      </UForm>

      <div class="mt-6 space-y-4">
        <USeparator />

        <UAlert color="error" variant="subtle" class="opacity-80">
          <template #description>
            Продолжая, вы принимаете
            <a class="underline" target="_blank" href="/terms">пользовательское соглашение</a>,
            <a class="underline" target="_blank" href="/oferta">публичную оферту</a> и даёте
            <a class="underline" target="_blank" href="/privacy_policy">согласие</a> на обработку
            персональных данных, а также подтверждаете, что владеете товаром на законных основаниях
            и купля-продажа происходит без нарушения
            <a class="underline" target="_blank" href="/zakon">закона Об оружии</a>.
          </template>
        </UAlert>

        <UButton
          v-if="state.step > 2"
          block
          color="error"
          variant="soft"
          icon="i-lucide-trash"
          @click="trashPost"
        >
          Начать заново
        </UButton>
      </div>
    </UContainer>
  </div>
</template>

<script setup lang="ts">
import { StorageSerializers, useStorage } from "@vueuse/core";
import * as Sentry from "@sentry/nuxt";
import { v4 as uuidv4 } from "uuid";
import { ModalsAlert } from "#components";

import type { Category } from "~/types/post";

definePageMeta({
  layout: "add"
});

const { isAuthenticated } = useSanctumAuth();
const { authOpenModal } = useDashboard();
const { $metrika } = useNuxtApp();
const client = useSanctumClient();
const cityStore = useCityStore();
const overlay = useOverlay();

const {
  state,
  resetState,
  registrationItems,
  attributes,
  schemaStepCategory,
  schemaStepTitle,
  schemaStepDescription,
  schemaStepPrice,
  validateStepAddress,
  validateStepImages,
  validateStepParameters,
  uploadImageAction,
  updatePostAction,
  getValues
} = usePostEdit();

const isPageLoaded = ref(false);
const isLoading = ref(false);
const isAutoSaveLoading = ref(true);
const isSuccessModal = ref(false);

useSeoMeta({
  title: "Добавить объявление о продаже оружия или снаряжения",
  ogTitle: "Добавить объявление о продаже оружия или снаряжения",
  description: "Продать оружие, подать бесплатное объявление о продаже оружия на ГанПост",
  ogDescription: "Продать оружие, подать бесплатное объявление о продаже оружия на ГанПост"
});

/**
 * В LocalStorage сохраняем uuid для
 * последующего продолжения
 */
const storagePost = useStorage<{
  step: number;
  slug: string | null;
  category: Category | undefined;
  uuid: string | undefined;
}>("post", null, undefined, { serializer: StorageSerializers.object });

async function uploadImages() {
  isLoading.value = false;

  const error = await uploadImageAction(state.uuid);
  if (error) useSanctumError(error);

  await nextStep();

  isLoading.value = false;
}

/**
 * Создает новый пост.
 */
async function createPost() {
  isLoading.value = true;
  const uuid = uuidv4();
  if (!state.category?.slug) {
    useToast().add({
      title: "Ошибка при создании объявления",
      description: "Выберите категорию",
      color: "error"
    });
    isLoading.value = false;
    return;
  }

  try {
    const data = await client("/post", {
      method: "POST",
      body: {
        title: state.title,
        category: state.category.slug,
        uuid
      }
    });

    state.uuid = uuid;
    state.slug = data.slug;
    attributes.value = data.attrs;
    $metrika.reachGoal("add_step1");
  } catch (error) {
    useSanctumError(error);
    useToast().add({
      title: "Ошибка при создании объявления",
      description: error?.data?.message ?? "Произошла ошибка",
      color: "error"
    });
    Sentry.captureException(error);
  }

  isLoading.value = false;
  await nextStep();
}

/**
 * Устанавливает текущий шаг.
 * @param {number} stepNumber - Номер шага.
 */
function setStep(stepNumber: number): void {
  state.step = stepNumber;
  nextTick(() => scrollToSection(`step-${state.step}`));
}

async function updatePost(is_final?: boolean) {
  isAutoSaveLoading.value = true;

  await updatePostAction(is_final);
  storagePost.value = {
    step: state.step,
    slug: state.slug,
    category: state?.category,
    uuid: state.uuid
  };

  isAutoSaveLoading.value = false;
}

/**
 * Переходит к следующему шагу.
 */
async function nextStep() {
  if (state.step === 2) {
    $metrika.reachGoal("add_step1"); // Нажал продолжить (параметры объявления)
  }
  if (state.step === 3) {
    $metrika.reachGoal("add_step2"); // Нажал Продолжить (место осмотра)
  }
  if (state.step === 4) {
    $metrika.reachGoal("add_step3"); // Продолжить (текст объявления)
  }
  if (state.step === 5) {
    $metrika.reachGoal("add_step4"); // Продолжить (фото)
  }
  if (state.step === 6) {
    // await uploadImagePost();
    $metrika.reachGoal("add_step5"); // Продолжить (цена)
  }

  state.step = state.step + 1;

  await updatePost();
  await nextTick(() => scrollToSection(`step-${state.step}`));
}

/**
 * Финальный шаг после всех проверок.
 */
async function finalStep() {
  await updatePost(true);

  if (!isAuthenticated.value) {
    authOpenModal.value = true;
    return;
  }

  trashPostAction();

  isSuccessModal.value = true;
  isLoading.value = false;
}

/**
 * Открывает модальное окно для подтверждения удаления поста.
 */
function trashPost() {
  overlay
    .create(ModalsAlert, {
      props: {
        title: "Начать заново?",
        description: "Вы потеряете все введенные данные.",
        actions: [
          {
            label: "Да, начать заново",
            color: "error",
            icon: "i-lucide-trash",
            onClick: () => {
              trashPostAction();
            }
          }
        ]
      }
    })
    .open();
}

/**
 * Очищает сохраненный черновик поста и возвращает на первый шаг.
 */
function trashPostAction() {
  resetState();
  storagePost.value = null;
  setStep(1);

  $metrika.reachGoal("add_reset");
}

/**
 * Загружает пост по его slug и uuid.
 * @param {string} slug - Slug поста.
 * @param {string} uuid - UUID поста у не авторизованного пользователя.
 */
async function getPost(slug: string, uuid: string) {
  if (!uuid) return trashPostAction();

  const data = await client(`/post/${slug}`, {
    params: {
      uuid
    }
  });

  for (const [key, value] of Object.entries(data.post)) {
    if (key !== "attributes" && key in state) {
      (state as Record<string, unknown>)[key] = value;
    }
  }

  for (const [key, value] of Object.entries(data.attributes)) {
    if (typeof value === "string") {
      state.attributes[key] = value;
    }
  }

  if (!state.city) {
    state.city = cityStore.city;
  }

  state.uuid = uuid;
  attributes.value = data.attrs;
}

/**
 * Прокручивает страницу к указанному шагу.
 * @param {string} id - ID элемента для прокрутки.
 */
function scrollToSection(id: string): void {
  const element = document.getElementById(id);
  if (element && typeof window !== "undefined") {
    element.scrollIntoView({ behavior: "smooth" });
  }
}

/**
 * Инициализация страницы, загрузка данных из localStorage.
 */
onMounted(async () => {
  isPageLoaded.value = false;

  // Загружаем города, если они еще не загружены
  if (!cityStore?.cities) {
    await cityStore?.fetch();
  }

  if (storagePost?.value?.slug && storagePost?.value?.uuid) {
    state.step = storagePost.value?.step ?? 1;

    try {
      await getPost(storagePost.value.slug, storagePost.value.uuid);
      const cityValue = state.city?.value;

      if (cityValue) {
        const foundCity = cityStore?.cities?.find((c) => c.value === cityValue);

        if (foundCity) {
          state.city = foundCity;
        } else {
          state.city = cityStore?.city;
        }
      }
    } catch (error) {
      Sentry.captureException(error);
      trashPostAction();
    }
  }

  isPageLoaded.value = true;

  if (state?.step) {
    if (state.step > 7) {
      state.step = 7;
    }
    await nextTick(() => setStep(state.step));
  }
});
</script>
