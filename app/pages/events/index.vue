<template>
  <UContainer>
    <CalendarMonth />
    <div v-if="false" class="relative h-full w-full overflow-scroll">
      <div>
        <div>
          <div class="grid grid-cols-7">
            <div class="flex items-center justify-center py-2" style="transform: none">
              <span class="text-t-quaternary text-xs font-medium">Sun</span>
            </div>
            <div class="flex items-center justify-center py-2" style="transform: none">
              <span class="text-t-quaternary text-xs font-medium">Mon</span>
            </div>
            <div class="flex items-center justify-center py-2" style="transform: none">
              <span class="text-t-quaternary text-xs font-medium">Tue</span>
            </div>
            <div class="flex items-center justify-center py-2" style="transform: none">
              <span class="text-t-quaternary text-xs font-medium">Wed</span>
            </div>
            <div class="flex items-center justify-center py-2" style="transform: none">
              <span class="text-t-quaternary text-xs font-medium">Thu</span>
            </div>
            <div class="flex items-center justify-center py-2" style="transform: none">
              <span class="text-t-quaternary text-xs font-medium">Fri</span>
            </div>
            <div class="flex items-center justify-center py-2" style="transform: none">
              <span class="text-t-quaternary text-xs font-medium">Sat</span>
            </div>
          </div>
          <div class="grid grid-cols-7 overflow-hidden">
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l-0"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="text-muted-foreground mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >29</span
                >
                <div
                  class="flex h-6 gap-1 px-2 opacity-50 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0"
                >
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              v-for="n in 30"
              :key="n"
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <div class="lg:flex-1" style="transform: none">
                  <div
                    class="size-2 rounded-full bg-orange-600 lg:hidden dark:bg-orange-500"
                    style="transform: none"
                  ></div>
                  <div class="cursor-grab" draggable="true">
                    <div
                      role="button"
                      tabindex="0"
                      class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-orange-200 bg-orange-50 px-2 text-xs whitespace-nowrap text-orange-700 select-none lg:flex dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300"
                      type="button"
                      aria-haspopup="dialog"
                      aria-expanded="false"
                      aria-controls="radix-«r32c»"
                      data-state="closed"
                      data-slot="dialog-trigger"
                    >
                      <div class="flex items-center gap-1.5 truncate">
                        <p class="flex-1 truncate font-semibold">Lunch with a colleague</p>
                      </div>
                      <span>12:00</span>
                    </div>
                  </div>
                </div>
                <div class="lg:flex-1" style="transform: none">
                  <div
                    class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                    style="transform: none"
                  ></div>
                  <div class="cursor-grab" draggable="true">
                    <div
                      role="button"
                      tabindex="0"
                      class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                      type="button"
                      aria-haspopup="dialog"
                      aria-expanded="false"
                      aria-controls="radix-«r32f»"
                      data-state="closed"
                      data-slot="dialog-trigger"
                    >
                      <div class="flex items-center gap-1.5 truncate">
                        <p class="flex-1 truncate font-semibold">Cooking class</p>
                      </div>
                      <span>15:55</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >1</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-orange-600 lg:hidden dark:bg-orange-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 ml-0 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md rounded-l-none border border-l-0 border-orange-200 bg-orange-50 px-2 text-xs whitespace-nowrap text-orange-700 select-none lg:flex dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r315»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate"></div>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-red-600 lg:hidden dark:bg-red-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-red-200 bg-red-50 px-2 text-xs whitespace-nowrap text-red-700 select-none lg:flex dark:border-red-800 dark:bg-red-950 dark:text-red-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r318»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">System upgrade</p>
                        </div>
                        <span>12:22</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r31b»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Sporting event</p>
                        </div>
                        <span>14:35</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="text-muted-foreground my-2 h-4.5 px-1.5 text-end text-xs font-semibold"
                  style="transform: none"
                >
                  <span
                    class="cursor-pointer"
                    type="button"
                    aria-haspopup="dialog"
                    aria-expanded="false"
                    aria-controls="radix-«r31e»"
                    data-state="closed"
                    data-slot="dialog-trigger"
                    ><span class="sm:hidden">+1</span
                    ><span class="my-1 hidden rounded-xl border px-2 py-0.5 sm:inline"
                      >1<span class="mx-1">more...</span></span
                    ></span
                  >
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >2</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-blue-600 lg:hidden dark:bg-blue-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-blue-200 bg-blue-50 px-2 text-xs whitespace-nowrap text-blue-700 select-none lg:flex dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r31h»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Cloud migration</p>
                        </div>
                        <span>11:08</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-purple-600 lg:hidden dark:bg-purple-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-purple-200 bg-purple-50 px-2 text-xs whitespace-nowrap text-purple-700 select-none lg:flex dark:border-purple-800 dark:bg-purple-950 dark:text-purple-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r31k»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Budget review</p>
                        </div>
                        <span>22:35</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >3</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r31n»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Nutrition consultation</p>
                        </div>
                        <span>13:02</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >4</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r31q»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Vacation planning</p>
                        </div>
                        <span>13:41</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-blue-600 lg:hidden dark:bg-blue-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-blue-200 bg-blue-50 px-2 text-xs whitespace-nowrap text-blue-700 select-none lg:flex dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r31t»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Job interview</p>
                        </div>
                        <span>16:29</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r320»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Graduation ceremony</p>
                        </div>
                        <span>21:02</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >5</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r323»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">College application deadline</p>
                        </div>
                        <span>12:49</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r326»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Investor pitch</p>
                        </div>
                        <span>14:42</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l-0"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >6</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-orange-600 lg:hidden dark:bg-orange-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-orange-200 bg-orange-50 px-2 text-xs whitespace-nowrap text-orange-700 select-none lg:flex dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r329»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Investor pitch</p>
                        </div>
                        <span>11:05</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-orange-600 lg:hidden dark:bg-orange-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-orange-200 bg-orange-50 px-2 text-xs whitespace-nowrap text-orange-700 select-none lg:flex dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r32c»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Lunch with a colleague</p>
                        </div>
                        <span>12:00</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r32f»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Cooking class</p>
                        </div>
                        <span>15:55</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >7</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >8</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-red-600 lg:hidden dark:bg-red-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-red-200 bg-red-50 px-2 text-xs whitespace-nowrap text-red-700 select-none lg:flex dark:border-red-800 dark:bg-red-950 dark:text-red-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r32i»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Grocery shopping</p>
                        </div>
                        <span>17:36</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >9</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r32l»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Home renovation meeting</p>
                        </div>
                        <span>17:47</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-blue-600 lg:hidden dark:bg-blue-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-blue-200 bg-blue-50 px-2 text-xs whitespace-nowrap text-blue-700 select-none lg:flex dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r32o»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Internship orientation</p>
                        </div>
                        <span>19:55</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >10</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="[&amp;&gt;span]:mr-2.5 relative z-10 mx-1 mr-0 hidden size-auto h-6.5 w-[calc(100%_+_1px)] items-center justify-between gap-1.5 truncate rounded-md rounded-r-none border border-r-0 border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r32r»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Performance review</p>
                        </div>
                        <span>14:37</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-orange-600 lg:hidden dark:bg-orange-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="[&amp;&gt;span]:mr-2.5 relative z-10 mx-1 mr-0 hidden size-auto h-6.5 w-[calc(100%_+_1px)] items-center justify-between gap-1.5 truncate rounded-md rounded-r-none border border-r-0 border-orange-200 bg-orange-50 px-2 text-xs whitespace-nowrap text-orange-700 select-none lg:flex dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r32u»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Financial planning session</p>
                        </div>
                        <span>21:28</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >11</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="relative z-10 mx-0 hidden size-auto h-6.5 w-[calc(100%_+_1px)] items-center justify-between gap-1.5 truncate rounded-none border border-x-0 border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r331»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate"></div>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-orange-600 lg:hidden dark:bg-orange-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 ml-0 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md rounded-l-none border border-l-0 border-orange-200 bg-orange-50 px-2 text-xs whitespace-nowrap text-orange-700 select-none lg:flex dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r334»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate"></div>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-orange-600 lg:hidden dark:bg-orange-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-orange-200 bg-orange-50 px-2 text-xs whitespace-nowrap text-orange-700 select-none lg:flex dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r337»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">College application deadline</p>
                        </div>
                        <span>13:24</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="text-muted-foreground my-2 h-4.5 px-1.5 text-end text-xs font-semibold"
                  style="transform: none"
                >
                  <span
                    class="cursor-pointer"
                    type="button"
                    aria-haspopup="dialog"
                    aria-expanded="false"
                    aria-controls="radix-«r33a»"
                    data-state="closed"
                    data-slot="dialog-trigger"
                    ><span class="sm:hidden">+2</span
                    ><span class="my-1 hidden rounded-xl border px-2 py-0.5 sm:inline"
                      >2<span class="mx-1">more...</span></span
                    ></span
                  >
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >12</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 ml-0 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md rounded-l-none border border-l-0 border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r33d»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate"></div>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r33g»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Fishing trip</p>
                        </div>
                        <span>16:28</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l-0"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >13</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r33j»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Physical therapy session</p>
                        </div>
                        <span>11:50</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r33m»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Investor pitch</p>
                        </div>
                        <span>14:08</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >14</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-blue-600 lg:hidden dark:bg-blue-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-blue-200 bg-blue-50 px-2 text-xs whitespace-nowrap text-blue-700 select-none lg:flex dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r33p»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Sales call</p>
                        </div>
                        <span>12:17</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >15</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="bg-primary text-primary-foreground mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >16</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r33s»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Sales call</p>
                        </div>
                        <span>13:35</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >17</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  style="transform: none"
                  >18</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r33v»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Birthday party</p>
                        </div>
                        <span>17:39</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r342»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Hotel check-in</p>
                        </div>
                        <span>19:34</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >19</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l-0"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >20</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-red-600 lg:hidden dark:bg-red-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="[&amp;&gt;span]:mr-2.5 relative z-10 mx-1 mr-0 hidden size-auto h-6.5 w-[calc(100%_+_1px)] items-center justify-between gap-1.5 truncate rounded-md rounded-r-none border border-r-0 border-red-200 bg-red-50 px-2 text-xs whitespace-nowrap text-red-700 select-none lg:flex dark:border-red-800 dark:bg-red-950 dark:text-red-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r345»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Cloud migration</p>
                        </div>
                        <span>22:32</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >21</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-red-600 lg:hidden dark:bg-red-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 ml-0 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md rounded-l-none border border-l-0 border-red-200 bg-red-50 px-2 text-xs whitespace-nowrap text-red-700 select-none lg:flex dark:border-red-800 dark:bg-red-950 dark:text-red-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r348»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate"></div>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >22</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >23</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-blue-600 lg:hidden dark:bg-blue-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-blue-200 bg-blue-50 px-2 text-xs whitespace-nowrap text-blue-700 select-none lg:flex dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r34b»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Book club meeting</p>
                        </div>
                        <span>19:29</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >24</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-purple-600 lg:hidden dark:bg-purple-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="[&amp;&gt;span]:mr-2.5 relative z-10 mx-1 mr-0 hidden size-auto h-6.5 w-[calc(100%_+_1px)] items-center justify-between gap-1.5 truncate rounded-md rounded-r-none border border-r-0 border-purple-200 bg-purple-50 px-2 text-xs whitespace-nowrap text-purple-700 select-none lg:flex dark:border-purple-800 dark:bg-purple-950 dark:text-purple-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r34e»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Performance review</p>
                        </div>
                        <span>19:46</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r34h»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Fishing trip</p>
                        </div>
                        <span>15:37</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >25</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-purple-600 lg:hidden dark:bg-purple-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="relative z-10 mx-0 hidden size-auto h-6.5 w-[calc(100%_+_1px)] items-center justify-between gap-1.5 truncate rounded-none border border-x-0 border-purple-200 bg-purple-50 px-2 text-xs whitespace-nowrap text-purple-700 select-none lg:flex dark:border-purple-800 dark:bg-purple-950 dark:text-purple-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r34k»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate"></div>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-orange-600 lg:hidden dark:bg-orange-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-orange-200 bg-orange-50 px-2 text-xs whitespace-nowrap text-orange-700 select-none lg:flex dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r34n»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Job interview</p>
                        </div>
                        <span>15:20</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r34q»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Board meeting</p>
                        </div>
                        <span>16:32</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >26</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-purple-600 lg:hidden dark:bg-purple-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="relative z-10 mx-0 hidden size-auto h-6.5 w-[calc(100%_+_1px)] items-center justify-between gap-1.5 truncate rounded-none border border-x-0 border-purple-200 bg-purple-50 px-2 text-xs whitespace-nowrap text-purple-700 select-none lg:flex dark:border-purple-800 dark:bg-purple-950 dark:text-purple-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r34t»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate"></div>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-orange-600 lg:hidden dark:bg-orange-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-orange-200 bg-orange-50 px-2 text-xs whitespace-nowrap text-orange-700 select-none lg:flex dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r350»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Nutrition consultation</p>
                        </div>
                        <span>11:56</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-orange-600 lg:hidden dark:bg-orange-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-orange-200 bg-orange-50 px-2 text-xs whitespace-nowrap text-orange-700 select-none lg:flex dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r353»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Fishing trip</p>
                        </div>
                        <span>14:59</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="text-muted-foreground my-2 h-4.5 px-1.5 text-end text-xs font-semibold"
                  style="transform: none"
                >
                  <span
                    class="cursor-pointer"
                    type="button"
                    aria-haspopup="dialog"
                    aria-expanded="false"
                    aria-controls="radix-«r356»"
                    data-state="closed"
                    data-slot="dialog-trigger"
                    ><span class="sm:hidden">+1</span
                    ><span class="my-1 hidden rounded-xl border px-2 py-0.5 sm:inline"
                      >1<span class="mx-1">more...</span></span
                    ></span
                  >
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l-0"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >27</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-purple-600 lg:hidden dark:bg-purple-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="relative z-10 mx-0 hidden size-auto h-6.5 w-[calc(100%_+_1px)] items-center justify-between gap-1.5 truncate rounded-none border border-x-0 border-purple-200 bg-purple-50 px-2 text-xs whitespace-nowrap text-purple-700 select-none lg:flex dark:border-purple-800 dark:bg-purple-950 dark:text-purple-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r359»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate"></div>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >28</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-purple-600 lg:hidden dark:bg-purple-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 ml-0 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md rounded-l-none border border-l-0 border-purple-200 bg-purple-50 px-2 text-xs whitespace-nowrap text-purple-700 select-none lg:flex dark:border-purple-800 dark:bg-purple-950 dark:text-purple-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r35c»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate"></div>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >29</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="[&amp;&gt;span]:mr-2.5 relative z-10 mx-1 mr-0 hidden size-auto h-6.5 w-[calc(100%_+_1px)] items-center justify-between gap-1.5 truncate rounded-md rounded-r-none border border-r-0 border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r35f»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Product launch</p>
                        </div>
                        <span>22:36</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r35i»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Marketing strategy review</p>
                        </div>
                        <span>13:09</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >30</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 ml-0 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md rounded-l-none border border-l-0 border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r35l»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate"></div>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-green-600 lg:hidden dark:bg-green-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-green-200 bg-green-50 px-2 text-xs whitespace-nowrap text-green-700 select-none lg:flex dark:border-green-800 dark:bg-green-950 dark:text-green-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r35o»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Team building activity</p>
                        </div>
                        <span>13:02</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >31</span
                >
                <div class="flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0">
                  <div class="lg:flex-1" style="transform: none">
                    <div
                      class="size-2 rounded-full bg-yellow-600 lg:hidden dark:bg-yellow-500"
                      style="transform: none"
                    ></div>
                    <div class="cursor-grab" draggable="true">
                      <div
                        role="button"
                        tabindex="0"
                        class="mx-1 hidden size-auto h-6.5 items-center justify-between gap-1.5 truncate rounded-md border border-yellow-200 bg-yellow-50 px-2 text-xs whitespace-nowrap text-yellow-700 select-none lg:flex dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300"
                        type="button"
                        aria-haspopup="dialog"
                        aria-expanded="false"
                        aria-controls="radix-«r35r»"
                        data-state="closed"
                        data-slot="dialog-trigger"
                      >
                        <div class="flex items-center gap-1.5 truncate">
                          <p class="flex-1 truncate font-semibold">Home renovation meeting</p>
                        </div>
                        <span>20:07</span>
                      </div>
                    </div>
                  </div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="text-muted-foreground mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >1</span
                >
                <div
                  class="flex h-6 gap-1 px-2 opacity-50 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0"
                >
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
            <div
              class="flex min-h-[180px] flex-col gap-1 border-t border-l"
              style="transform: none"
            >
              <div class="h-full w-full py-2">
                <span
                  class="text-muted-foreground mb-1 flex h-6 w-6 translate-x-1 items-center justify-center rounded-full px-1 text-xs font-semibold lg:px-2"
                  >2</span
                >
                <div
                  class="flex h-6 gap-1 px-2 opacity-50 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0"
                >
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                  <div class="lg:flex-1" style="transform: none"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div  v-if="false">
      <div>
        <h1 class="mt-4 text-2xl font-bold">Календарь событий</h1>
        <p class="text-md my-2">
          Календарь событий. Анонсы мероприятий стрелкового спорта, соревнований, выставок и других
          событий
        </p>
      </div>
      <div class="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <UBlogPost
          v-for="event in events?.data"
          :key="event.slug"
          :to="`/events/${event.slug}`"
          :title="event.title"
          :description="event.description"
          :image="event.cover"
          :authors="[
            {
              name: event.place
            }
          ]"
        >
          <template #date>
            {{ event.start_date }}
          </template>
        </UBlogPost>
      </div>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
const client = useSanctumClient();
const route = useRoute();
const page = ref(route.query.page ? Number(route.query.page) : 1);

const { data: events } = await useAsyncData(
  "events",
  () =>
    client("/events", {
      params: {
        page: page.value
      }
    }),
  {
    watch: [page]
  }
);

if (route.query.page === "1") {
  useHead(() => ({
    link: [
      {
        rel: "canonical",
        href: "https://gunpost.ru/events"
      }
    ]
  }));
}

let title = "Календарь событий";
if (page.value > 1) {
  title += `. Стр. ${page.value}`;
}

useSeoMeta({
  title: title,
  ogTitle: title,
  description: "Анонсы мероприятий стрелкового спорта, соревнований, выставок и других событий",
  ogDescription: "Анонсы мероприятий стрелкового спорта, соревнований, выставок и других событий"
});
</script>
