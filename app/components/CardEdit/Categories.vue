<template>
  <UCard :ui="{ body: 'space-y-4' }">
    <UFormField label="Категория:" size="xl" required>
      <div class="mt-4">
        <USelectMenu
          placeholder="Выберите категорию..."
          color="neutral"
          autofocus
          :items="categories"
          class="w-full"
          :search-input="{ placeholder: 'Поиск категории...', icon: 'i-lucide-search' }"
          :ui="{
            trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200'
          }"
          @update:model-value="selectCategory"
        >
        </USelectMenu>
      </div>
    </UFormField>
    <template v-if="is_active" #footer>
      <div class="flex gap-2">
        <UButton
          size="xl"
          type="submit"
          color="success"
          :disabled="!modelValue"
          @click="selectCategory(modelValue)"
        >
          Продолжить
        </UButton>
      </div>
    </template>
  </UCard>
</template>

<script setup lang="ts">
import type { Category } from "~/types/post";

defineProps<{
  modelValue: Category | undefined;
  is_active: boolean | false;
}>();
const emit = defineEmits(["update:modelValue"]);
const { categories } = useDashboard();
const selectCategory = (item: {
  label: string;
  category: string;
  registration?: boolean;
  image: string;
  colSpan: string;
}) => {
  console.log(item);
  emit("update:modelValue", {
    name: item.label,
    slug: item.category,
    registration: item.registration,
    value: item.category,
    label: item.label
  });
};
</script>
